import { StyleSheet, Dimensions } from 'react-native';

const { width, height } = Dimensions.get('window');

export const COLORS = {
  primary: '#E50914',
  text: '#FFFFFF',
  background: '#000000',
  overlay: 'rgba(0, 0, 0, 0.6)',
  bottomSheetBackground: '#141414',
  bottomSheetHandle: 'grey',
  textEmphasis: '#E50914'
};

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  loadingContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
  },
  videoPlayer: {
    ...StyleSheet.absoluteFillObject,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 1,
  },
  // --- Controls ---
  controlsContainer: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 2,
  },
  topControls: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 10,
  },
  topGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 100
  },
  videoTitle: {
    color: COLORS.text,
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
    textAlign: 'left',
    marginLeft: 15,
  },
  controlButton: {
    padding: 8,
  },
  centerControls: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -30 }, { translateY: -30 }],
    justifyContent: 'center',
    alignItems: 'center',
  },
  playPauseButton: {
    width: 60,
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
  bottomControls: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  bottomGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 120,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingBottom: 10
  },
  timeText: {
    color: COLORS.text,
    fontSize: 12,
  },
  progressBar: {
    flex: 1,
    height: 20,
    marginHorizontal: 10,
    justifyContent: 'center'
  },
  // --- Gesture Detection ---
  gestureArea: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 1
  },
  doubleTapArea: {
    flex: 1,
    flexDirection: 'row'
  },
  leftDoubleTap: {
    flex: 1
  },
  rightDoubleTap: {
    flex: 1
  },
  // --- Bottom Sheet Styles ---
  bottomSheetBackground: {
    backgroundColor: COLORS.bottomSheetBackground,
  },
  bottomSheetHandle: {
    backgroundColor: COLORS.bottomSheetHandle,
  },
  bottomSheetHeader: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#222',
  },
  bottomSheetTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  bottomSheetItem: {
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#252525'
  },
  bottomSheetItemText: {
    fontSize: 16,
    color: COLORS.text,
  },
  bottomSheetItemSelectedText: {
    fontWeight: 'bold',
    color: COLORS.primary,
  }
});