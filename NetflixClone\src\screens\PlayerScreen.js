import React, { useState, useEffect, useRef, useCallback } from 'react';
import { View, Text, TouchableOpacity, StatusBar, ActivityIndicator, Alert, BackHandler } from 'react-native';
import { useNavigation, useRoute, useFocusEffect } from '@react-navigation/native';
import { VideoView, useVideoPlayer, isPlaying } from 'expo-video';
import * as ScreenOrientation from 'expo-screen-orientation';
import { Gesture, GestureDetector, GestureHandlerRootView } from 'react-native-gesture-handler';
import Animated, { useSharedValue, useAnimatedStyle, withTiming, runOnJS } from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import BottomSheet, { BottomSheetFlatList } from '@gorhom/bottom-sheet';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';

// Assuming soraApi is correctly set up
import soraApi from '../services/soraApi';
// The styles are now in a separate file, make sure the path is correct
import { styles, COLORS } from '../styles/styles';

// --- Helper Functions ---
const formatTime = (s) => {
    s = Math.max(0, s);
    const m = Math.floor(s / 60);
    const h = Math.floor(m / 60);
    return h > 0 ?
        `${h}:${(m % 60).toString().padStart(2, '0')}:${Math.floor(s % 60).toString().padStart(2, '0')}` :
        `${m}:${Math.floor(s % 60).toString().padStart(2, '0')}`;
};

const PlayerScreen = () => {
    // --- Hooks & Refs ---
    const navigation = useNavigation();
    const route = useRoute();
    const insets = useSafeAreaInsets();
    const { item } = route.params;

    // --- State (moved before useVideoPlayer to avoid undefined setStatus) ---
    const [streamData, setStreamData] = useState({ streams: [], subtitles: [] });
    const [selectedQuality, setSelectedQuality] = useState(null);
    const [status, setStatus] = useState({ isPlaying: false, isBuffering: true, duration: 0, position: 0 });
    const [isLandscape, setIsLandscape] = useState(false);
    const [error, setError] = useState(null);

    const player = useVideoPlayer(null, (p) => {
        // Player status updates are handled here for stability
        setStatus({
            isPlaying: p.playing,
            isBuffering: p.status === 'loading',
            duration: p.duration || 0,
            position: p.currentTime || 0,
        });
    });

    const qualitySheetRef = useRef(null);
    const controlsTimeout = useRef(null);
    const isSeeking = useRef(false);

    // --- Animated Values ---
    const controlsOpacity = useSharedValue(1);

    // --- Data Loading Effect ---
    useEffect(() => {
        const loadInitialData = async () => {
            try {
                let data;
                if (item.media_type === 'tv' && item.season !== undefined && item.episode !== undefined) {
                    data = await soraApi.getTVStreams(item.id, item.season, item.episode);
                } else {
                    data = await soraApi.getMovieStreams(item.id);
                }
                const processed = soraApi.processStreamResponse(data);
                const bestStream = soraApi.getBestQualityStream(processed);

                if (!bestStream?.url) {
                    throw new Error('No playable streams were found.');
                }
                
                setStreamData({ streams: processed.streams, subtitles: data.subtitles || [] });
                setSelectedQuality(bestStream);
                await player.replace(bestStream.url);
                player.play();

            } catch (err) {
                console.error("Failed to load stream data:", err);
                setError(err.message);
            }
        };

        loadInitialData();
    }, [item.id, player]);

    // --- UI & Orientation Effects ---
    useFocusEffect(
        useCallback(() => {
            const orientationSubscription = ScreenOrientation.addOrientationChangeListener(
                (e) => setIsLandscape(e.orientationInfo.orientation > 2)
            );
            
            const backHandlerSubscription = BackHandler.addEventListener('hardwareBackPress', () => {
                if (isLandscape) {
                    ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.PORTRAIT_UP);
                    return true; // We've handled it
                }
                return false; // Let the default handler work
            });

            return () => {
                player.pause();
                ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.PORTRAIT_UP);
                orientationSubscription.remove();
                backHandlerSubscription.remove();
            };
        }, [player, isLandscape])
    );
    
    useEffect(() => {
        StatusBar.setHidden(isLandscape, 'fade');
    }, [isLandscape]);
    
    // Auto-hide controls effect
    useEffect(() => {
        if (status.isPlaying) {
            hideControls();
        } else {
            showControls(true);
        }
    }, [status.isPlaying]);

    // --- Control Visibility Functions ---
    const hideControls = () => {
        clearTimeout(controlsTimeout.current);
        controlsTimeout.current = setTimeout(() => {
            controlsOpacity.value = withTiming(0, { duration: 300 });
        }, 4000);
    };

    const showControls = (permanent = false) => {
        clearTimeout(controlsTimeout.current);
        controlsOpacity.value = withTiming(1, { duration: 200 });
        if (!permanent && status.isPlaying) {
            hideControls();
        }
    };
    
    const toggleControls = useCallback(() => {
      runOnJS(controlsOpacity.value > 0 ? hideControls : showControls)();
    }, []);

    // --- Player Actions ---
    const changeQuality = async (newQuality) => {
        if (!newQuality || newQuality.url === selectedQuality?.url) return;
        
        qualitySheetRef.current?.close();
        setStatus(s => ({ ...s, isBuffering: true }));
        
        const currentPosition = player.currentTime;
        await player.replace(newQuality.url);
        player.currentTime = currentPosition;
        player.play();

        setSelectedQuality(newQuality);
    };

    // --- Gestures ---
    const singleTap = Gesture.Tap().maxDuration(250).onStart(toggleControls);
    const doubleTapLeft = Gesture.Tap().numberOfTaps(2).onStart(() => runOnJS(player.seekBy)(-10));
    const doubleTapRight = Gesture.Tap().numberOfTaps(2).onStart(() => runOnJS(player.seekBy)(10));
    
    const animatedControlsStyle = useAnimatedStyle(() => ({ opacity: controlsOpacity.value }));
    
    // --- Render Logic ---
    if (error) {
        return (
            <View style={styles.loadingContainer}>
                <Text style={{color: 'white', textAlign: 'center', margin: 20}}>{error}</Text>
                <TouchableOpacity onPress={() => navigation.goBack()}><Text style={{color: COLORS.primary, fontSize: 16}}>Go Back</Text></TouchableOpacity>
            </View>
        );
    }
    
    if (!selectedQuality && !error) {
        return <View style={styles.loadingContainer}><ActivityIndicator size="large" color={COLORS.primary} /></View>;
    }

    return (
        <GestureHandlerRootView style={styles.container}>
            <StatusBar hidden={isLandscape} animated />

            <VideoView player={player} style={styles.videoPlayer} contentFit="contain" />
            
            <GestureDetector gesture={singleTap}>
              <View style={styles.gestureArea}>
                <GestureDetector gesture={Gesture.Exclusive(doubleTapRight, doubleTapLeft)}>
                    <View style={styles.doubleTapArea}>
                        <View style={styles.leftDoubleTap} />
                        <View style={styles.rightDoubleTap} />
                    </View>
                </GestureDetector>
              </View>
            </GestureDetector>

            {(status.isBuffering || (player.status === 'loading')) && (
                <View style={[styles.loadingContainer, { backgroundColor: 'transparent' }]}><ActivityIndicator size="large" color="white" /></View>
            )}

            <Animated.View style={[styles.controlsContainer, animatedControlsStyle]} pointerEvents={controlsOpacity.value > 0 ? 'auto' : 'none'}>
                {/* TOP */}
                <LinearGradient colors={[COLORS.overlay, 'transparent']} style={[styles.topGradient, { height: insets.top + 60 }]}/>
                <View style={[styles.topControls, { marginTop: insets.top, paddingLeft: isLandscape ? insets.left + 10 : 10, paddingRight: isLandscape ? insets.right + 10 : 10 }]}>
                    <TouchableOpacity style={styles.controlButton} onPress={() => navigation.goBack()}><Ionicons name="arrow-back" size={26} color="white"/></TouchableOpacity>
                    <Text style={styles.videoTitle} numberOfLines={1}>{item.title || item.name}</Text>
                    <TouchableOpacity style={styles.controlButton} onPress={() => qualitySheetRef.current?.snapToIndex(0)}><Ionicons name="settings-outline" size={24} color="white"/></TouchableOpacity>
                </View>

                {/* MIDDLE */}
                <TouchableOpacity style={styles.playPauseButton} onPress={() => player.playing ? player.pause() : player.play()}>
                    <Ionicons name={status.isPlaying ? 'pause' : 'play'} size={50} color="white"/>
                </TouchableOpacity>

                {/* BOTTOM */}
                <LinearGradient colors={['transparent', COLORS.overlay]} style={[styles.bottomGradient, { height: insets.bottom + 90 }]} />
                <View style={[styles.bottomControls, { paddingBottom: isLandscape ? 10 : insets.bottom + 10, paddingLeft: isLandscape ? insets.left + 15 : 15, paddingRight: isLandscape ? insets.right + 15 : 15}]}>
                    <View style={styles.progressContainer}>
                        <Text style={styles.timeText}>{formatTime(status.position)}</Text>
                        <View style={styles.progressBar}>
                           <View style={{ flex: 1, height: 3, backgroundColor: 'rgba(255,255,255,0.4)', borderRadius: 3}}>
                             <View style={{height: '100%', width: `${(status.position / status.duration) * 100 || 0}%`, backgroundColor: COLORS.primary}} />
                           </View>
                        </View>
                        <Text style={styles.timeText}>{formatTime(status.duration)}</Text>
                        <TouchableOpacity 
                            style={styles.controlButton} 
                            onPress={() => ScreenOrientation.lockAsync(isLandscape ? ScreenOrientation.OrientationLock.PORTRAIT_UP : ScreenOrientation.OrientationLock.LANDSCAPE)}
                        >
                            <Ionicons name={isLandscape ? "contract" : "expand"} size={24} color="white" />
                        </TouchableOpacity>
                    </View>
                </View>
            </Animated.View>
            
            <BottomSheet
              ref={qualitySheetRef}
              index={-1} // Hidden by default
              snapPoints={['40%', '50%']}
              enablePanDownToClose={true}
              backgroundStyle={styles.bottomSheetBackground}
              handleIndicatorStyle={styles.bottomSheetHandle}
            >
              <View style={styles.bottomSheetHeader}><Text style={styles.bottomSheetTitle}>Select Quality</Text></View>
              <BottomSheetFlatList
                  data={streamData.streams}
                  keyExtractor={(q) => q.url}
                  renderItem={({ item: quality }) => (
                      <TouchableOpacity style={styles.bottomSheetItem} onPress={() => changeQuality(quality)}>
                          <Text style={[styles.bottomSheetItemText, selectedQuality?.url === quality.url && styles.bottomSheetItemSelectedText]}>
                              {quality.name}
                          </Text>
                      </TouchableOpacity>
                  )}
              />
            </BottomSheet>

        </GestureHandlerRootView>
    );
};

export default PlayerScreen;